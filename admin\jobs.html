<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Ed-admin Job Management - Admin Panel">
        <meta name="robots" content="noindex, nofollow">

        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="../assets/css/boxicons.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="../assets/css/style.css">

        <title>Ed-admin: Job Management - Admin Panel</title>
        <link rel="icon" type="image/png" href="../assets/img/favicon-Edadmin.ico">

        <!-- Custom Admin CSS -->
        <style>
            body {
                background-color: #ffffff;
                font-family: 'Poppins', sans-serif;
                color: #333333;
            }

            .admin-header {
                background: linear-gradient(135deg, #006EB3 0%, #0056a3 100%);
                color: white;
                padding: 20px 0;
                margin-bottom: 30px;
                box-shadow: 0 4px 15px rgba(0, 107, 179, 0.15);
            }

            .admin-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
                border: 1px solid #f0f0f0;
                border-top: 3px solid #FF9300;
            }

            .btn-primary {
                background: #006EB3;
                border: none;
                border-radius: 5px;
                padding: 13px 25px;
                font-weight: 500;
                font-size: 15px;
                transition: all 0.5s ease;
                position: relative;
                overflow: hidden;
                z-index: 1;
            }

            .btn-primary::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 0;
                height: 100%;
                background: #FF9300;
                transition: width 0.5s ease;
                z-index: -1;
            }

            .btn-primary:hover {
                background: #006EB3;
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 107, 179, 0.3);
            }

            .btn-primary:hover::before {
                width: 100%;
            }

            .btn-danger {
                background: #dc3545;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: 500;
                transition: all 0.3s ease;
            }

            .btn-danger:hover {
                background: #c82333;
                transform: translateY(-1px);
            }

            .btn-warning {
                background: #FF9300;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: 500;
                color: white;
                transition: all 0.3s ease;
            }

            .btn-warning:hover {
                background: #e6840a;
                color: white;
                transform: translateY(-1px);
            }

            .btn-secondary {
                background: #4a6f8a;
                border: none;
                border-radius: 5px;
                padding: 13px 25px;
                font-weight: 500;
                color: white;
                transition: all 0.3s ease;
            }

            .btn-secondary:hover {
                background: #3a5a75;
                color: white;
            }

            .btn-light {
                background: white;
                border: 2px solid #FF9300;
                border-radius: 5px;
                padding: 11px 23px;
                font-weight: 500;
                color: #FF9300;
                transition: all 0.3s ease;
            }

            .btn-light:hover {
                background: #FF9300;
                color: white;
            }

            .form-control {
                border-radius: 5px;
                border: 2px solid #e9ecef;
                padding: 12px 15px;
                transition: all 0.3s ease;
                font-family: 'Poppins', sans-serif;
            }

            .form-control:focus {
                border-color: #006EB3;
                box-shadow: 0 0 0 0.2rem rgba(0, 107, 179, 0.25);
            }

            .job-item {
                background: white;
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(8, 10, 60, 0.1);
                transition: all 0.3s ease;
                border-left: 4px solid #FF9300;
            }

            .job-item:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(8, 10, 60, 0.15);
                border-left-color: #006EB3;
            }

            .job-meta {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }

            .job-badge {
                background: #e8f4fd;
                color: #006EB3;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: 500;
            }

            .loading-spinner {
                display: inline-block;
                width: 20px;
                height: 20px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #006EB3;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .alert {
                border-radius: 10px;
                border: none;
                padding: 15px 20px;
            }

            .alert-success {
                background-color: #d4edda;
                color: #155724;
                border-left: 4px solid #28a745;
            }

            .alert-danger {
                background-color: #f8d7da;
                color: #721c24;
                border-left: 4px solid #dc3545;
            }

            .alert-warning {
                background-color: #fff3cd;
                color: #856404;
                border-left: 4px solid #FF9300;
            }

            .modal-content {
                border-radius: 15px;
                border: none;
            }

            .modal-header {
                background: linear-gradient(135deg, #006EB3 0%, #080a3c 100%);
                color: white;
                border-radius: 15px 15px 0 0;
            }

            .stats-card {
                background: linear-gradient(135deg, #006EB3 0%, #080a3c 100%);
                color: white;
                border-radius: 15px;
                padding: 25px;
                text-align: center;
                margin-bottom: 20px;
                box-shadow: 0 4px 15px rgba(0, 107, 179, 0.2);
            }

            .stats-card:nth-child(2) {
                background: linear-gradient(135deg, #FF9300 0%, #e6840a 100%);
            }

            .stats-card:nth-child(3) {
                background: linear-gradient(135deg, #4a6f8a 0%, #3a5a75 100%);
            }

            .stats-number {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 5px;
            }

            .stats-label {
                font-size: 1rem;
                opacity: 0.9;
            }

            h1, h2, h3, h4, h5, h6 {
                color: #080a3c;
                font-weight: 600;
            }

            label {
                color: #080a3c;
                font-weight: 500;
                margin-bottom: 8px;
            }

            .text-muted {
                color: #4a6f8a !important;
            }

            /* Ed-admin style section titles */
            .section-title h3 {
                position: relative;
                display: inline-block;
            }

            .section-title h3::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                width: 50px;
                height: 3px;
                background: #FF9300;
            }
        </style>
    </head>

    <body>
        <!-- Admin Header -->
        <div class="admin-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 style="margin: 0; font-weight: 600; color: white;">
                            <i class='bx bx-briefcase' style="color: white;"></i> Job Management
                        </h1>
                        <p style="margin: 5px 0 0 0; opacity: 0.9; color: white;">Manage job listings for Ed-admin careers page</p>
                    </div>
                    <div class="col-md-6 text-right">
                        <a href="../join-our-team.html" class="btn btn-light" target="_blank">
                            <i class='bx bx-external-link'></i> View Public Page
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <!-- Stats Section -->
            <div class="row">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number" id="total-jobs">0</div>
                        <div class="stats-label">Total Jobs</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #FF9300 0%, #e6840a 100%);">
                        <div class="stats-number" id="active-jobs">0</div>
                        <div class="stats-label">Active Listings</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #4a6f8a 0%, #3a5a75 100%);">
                        <div class="stats-number" id="departments">0</div>
                        <div class="stats-label">Departments</div>
                    </div>
                </div>
            </div>

            <!-- Add New Job Section -->
            <div class="admin-card">
                <div class="section-title">
                    <h3 style="margin-bottom: 25px;">
                        <i class='bx bx-plus-circle'></i> Add New Job
                    </h3>
                </div>

                <form id="job-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-title">Job Title *</label>
                                <input type="text" class="form-control" id="job-title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-department">Department *</label>
                                <select class="form-control" id="job-department" required>
                                    <option value="">Select Department</option>
                                    <option value="Engineering">Engineering</option>
                                    <option value="Product">Product</option>
                                    <option value="Design">Design</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Sales">Sales</option>
                                    <option value="Customer Success">Customer Success</option>
                                    <option value="HR">Human Resources</option>
                                    <option value="Finance">Finance</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-location">Location *</label>
                                <input type="text" class="form-control" id="job-location" placeholder="e.g., Remote / San Francisco" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="job-type">Job Type *</label>
                                <select class="form-control" id="job-type" required>
                                    <option value="">Select Type</option>
                                    <option value="Full-time">Full-time</option>
                                    <option value="Part-time">Part-time</option>
                                    <option value="Contract">Contract</option>
                                    <option value="Internship">Internship</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="job-description">Job Description *</label>
                        <textarea class="form-control" id="job-description" rows="4" placeholder="Describe the role, responsibilities, and what makes this position exciting..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="job-benefits">Benefits *</label>
                        <textarea class="form-control" id="job-benefits" rows="3" placeholder="Enter benefits separated by • (bullet). Example: Competitive salary• Health insurance• Flexible hours" required></textarea>
                        <small class="form-text text-muted">Separate each benefit with • (bullet symbol)</small>
                    </div>

                    <div class="form-group">
                        <label for="job-what-youll-do">What You'll Do *</label>
                        <textarea class="form-control" id="job-what-youll-do" rows="3" placeholder="Enter responsibilities separated by • (bullet). Example: Design applications• Lead team meetings• Review code" required></textarea>
                        <small class="form-text text-muted">Separate each responsibility with • (bullet symbol)</small>
                    </div>

                    <div class="form-group">
                        <label for="job-what-youll-need">What You'll Need *</label>
                        <textarea class="form-control" id="job-what-youll-need" rows="3" placeholder="Enter requirements separated by • (bullet). Example: 5+ years experience• Bachelor's degree• Strong communication skills" required></textarea>
                        <small class="form-text text-muted">Separate each requirement with • (bullet symbol)</small>
                    </div>

                    <div class="form-group">
                        <label for="job-requirements">Additional Requirements *</label>
                        <textarea class="form-control" id="job-requirements" rows="3" placeholder="Any additional requirements, certifications, or preferences..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="job-image">Image URL (Optional)</label>
                        <input type="url" class="form-control" id="job-image" placeholder="https://images.unsplash.com/...">
                        <small class="form-text text-muted">Leave empty to use default image</small>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <span id="submit-text">
                            <i class='bx bx-plus'></i> Add Job
                        </span>
                        <span id="submit-loading" style="display: none;">
                            <span class="loading-spinner"></span> Adding...
                        </span>
                    </button>
                    <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
                        <i class='bx bx-refresh'></i> Reset
                    </button>
                </form>
            </div>

            <!-- Jobs List Section -->
            <div class="admin-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="section-title">
                        <h3 style="margin: 0;">
                            <i class='bx bx-list-ul'></i> Current Job Listings
                        </h3>
                    </div>
                    <button class="btn btn-primary" onclick="loadJobs()">
                        <i class='bx bx-refresh'></i> Refresh
                    </button>
                </div>

                <!-- Loading State -->
                <div id="jobs-loading" style="text-align: center; padding: 40px;">
                    <div class="loading-spinner" style="width: 40px; height: 40px;"></div>
                    <p style="margin-top: 15px; color: #666;">Loading jobs...</p>
                </div>

                <!-- Error State -->
                <div id="jobs-error" style="display: none;">
                    <div class="alert alert-danger">
                        <h5><i class='bx bx-error'></i> Error Loading Jobs</h5>
                        <p>Unable to load job listings. Please check if the backend server is running.</p>
                    </div>
                </div>

                <!-- Jobs Container -->
                <div id="jobs-container">
                    <!-- Jobs will be loaded here -->
                </div>

                <!-- Empty State -->
                <div id="jobs-empty" style="display: none; text-align: center; padding: 40px;">
                    <i class='bx bx-briefcase' style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                    <h4 style="color: #666;">No Jobs Found</h4>
                    <p style="color: #999;">Start by adding your first job listing above.</p>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>

        <!-- Edit Job Modal -->
        <div class="modal fade" id="editJobModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class='bx bx-edit'></i> Edit Job
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="edit-job-form">
                            <input type="hidden" id="edit-job-id">
                            <!-- Form fields will be populated by JavaScript -->
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="updateJob()">
                            <span id="update-text">Update Job</span>
                            <span id="update-loading" style="display: none;">
                                <span class="loading-spinner"></span> Updating...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="../assets/js/jquery.min.js"></script>
        <script src="../assets/js/bootstrap.min.js"></script>

        <!-- Admin JavaScript -->
        <script>
            // API Configuration
            const API_BASE_URL = 'http://localhost:3001/api';
            const ADMIN_API_URL = `${API_BASE_URL}/admin/jobs`;
            const JOBS_API_URL = `${API_BASE_URL}/jobs`;

            // Global variables
            let currentEditingJobId = null;

            // Initialize page
            document.addEventListener('DOMContentLoaded', function() {
                loadJobs();
                setupEventListeners();
            });

            // Setup event listeners
            function setupEventListeners() {
                document.getElementById('job-form').addEventListener('submit', handleAddJob);
            }

            // Load and display jobs
            async function loadJobs() {
                try {
                    showJobsLoading();
                    hideJobsError();
                    hideJobsEmpty();

                    const response = await fetch(JOBS_API_URL);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const jobs = await response.json();

                    hideJobsLoading();

                    if (jobs.length === 0) {
                        showJobsEmpty();
                    } else {
                        displayJobs(jobs);
                    }

                    updateStats(jobs);

                } catch (error) {
                    console.error('Error loading jobs:', error);
                    hideJobsLoading();
                    showJobsError();
                }
            }

            // Display jobs in the list
            function displayJobs(jobs) {
                const container = document.getElementById('jobs-container');
                container.innerHTML = '';

                jobs.forEach(job => {
                    const jobElement = createJobElement(job);
                    container.appendChild(jobElement);
                });
            }

            // Create job element
            function createJobElement(job) {
                const jobDiv = document.createElement('div');
                jobDiv.className = 'job-item';
                jobDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="job-meta">
                                <span class="job-badge">${job.department}</span>
                                <span class="job-badge" style="background: #fff3e0; color: #FF9300;">${job.type}</span>
                            </div>
                            <h5 style="color: #080a3c; margin-bottom: 8px;">${job.title}</h5>
                            <p style="color: #4a6f8a; margin-bottom: 10px;">
                                <i class='bx bx-map'></i> ${job.location}
                            </p>
                            <p style="color: #4a6f8a; margin-bottom: 15px; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                                ${job.description}
                            </p>
                            <small style="color: #4a6f8a;">
                                <i class='bx bx-time'></i> Created: ${new Date(job.created_at).toLocaleDateString()}
                            </small>
                        </div>
                        <div class="ml-3">
                            <button class="btn btn-warning btn-sm mb-2" onclick="editJob(${job.id})" title="Edit Job">
                                <i class='bx bx-edit'></i>
                            </button>
                            <br>
                            <button class="btn btn-danger btn-sm" onclick="deleteJob(${job.id}, '${job.title.replace(/'/g, "\\'")}')">
                                <i class='bx bx-trash'></i>
                            </button>
                        </div>
                    </div>
                `;

                return jobDiv;
            }

            // Handle add job form submission
            async function handleAddJob(event) {
                event.preventDefault();

                const formData = {
                    title: document.getElementById('job-title').value.trim(),
                    department: document.getElementById('job-department').value,
                    location: document.getElementById('job-location').value.trim(),
                    type: document.getElementById('job-type').value,
                    description: document.getElementById('job-description').value.trim(),
                    benefits: document.getElementById('job-benefits').value.trim(),
                    what_youll_do: document.getElementById('job-what-youll-do').value.trim(),
                    what_youll_need: document.getElementById('job-what-youll-need').value.trim(),
                    requirements: document.getElementById('job-requirements').value.trim(),
                    image_url: document.getElementById('job-image').value.trim()
                };

                // Validation
                if (!formData.title || !formData.department || !formData.location ||
                    !formData.type || !formData.description || !formData.benefits ||
                    !formData.what_youll_do || !formData.what_youll_need || !formData.requirements) {
                    showMessage('Please fill in all required fields.', 'danger');
                    return;
                }

                try {
                    showSubmitLoading();

                    const response = await fetch(ADMIN_API_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to create job');
                    }

                    showMessage('Job created successfully!', 'success');
                    resetForm();
                    loadJobs(); // Reload the jobs list

                } catch (error) {
                    console.error('Error creating job:', error);
                    showMessage('Error creating job: ' + error.message, 'danger');
                } finally {
                    hideSubmitLoading();
                }
            }

            // Edit job
            async function editJob(jobId) {
                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Failed to fetch job details');
                    }

                    const job = await response.json();
                    currentEditingJobId = jobId;

                    // Populate edit form
                    populateEditForm(job);

                    // Show modal
                    $('#editJobModal').modal('show');

                } catch (error) {
                    console.error('Error fetching job:', error);
                    showMessage('Error loading job details: ' + error.message, 'danger');
                }
            }

            // Populate edit form
            function populateEditForm(job) {
                const modalBody = document.querySelector('#editJobModal .modal-body form');
                modalBody.innerHTML = `
                    <input type="hidden" id="edit-job-id" value="${job.id}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Job Title *</label>
                                <input type="text" class="form-control" id="edit-job-title" value="${job.title}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Department *</label>
                                <select class="form-control" id="edit-job-department" required>
                                    <option value="Engineering" ${job.department === 'Engineering' ? 'selected' : ''}>Engineering</option>
                                    <option value="Product" ${job.department === 'Product' ? 'selected' : ''}>Product</option>
                                    <option value="Design" ${job.department === 'Design' ? 'selected' : ''}>Design</option>
                                    <option value="Marketing" ${job.department === 'Marketing' ? 'selected' : ''}>Marketing</option>
                                    <option value="Sales" ${job.department === 'Sales' ? 'selected' : ''}>Sales</option>
                                    <option value="Customer Success" ${job.department === 'Customer Success' ? 'selected' : ''}>Customer Success</option>
                                    <option value="HR" ${job.department === 'HR' ? 'selected' : ''}>Human Resources</option>
                                    <option value="Finance" ${job.department === 'Finance' ? 'selected' : ''}>Finance</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Location *</label>
                                <input type="text" class="form-control" id="edit-job-location" value="${job.location}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Job Type *</label>
                                <select class="form-control" id="edit-job-type" required>
                                    <option value="Full-time" ${job.type === 'Full-time' ? 'selected' : ''}>Full-time</option>
                                    <option value="Part-time" ${job.type === 'Part-time' ? 'selected' : ''}>Part-time</option>
                                    <option value="Contract" ${job.type === 'Contract' ? 'selected' : ''}>Contract</option>
                                    <option value="Internship" ${job.type === 'Internship' ? 'selected' : ''}>Internship</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Job Description *</label>
                        <textarea class="form-control" id="edit-job-description" rows="4" required>${job.description}</textarea>
                    </div>
                    <div class="form-group">
                        <label>Benefits *</label>
                        <textarea class="form-control" id="edit-job-benefits" rows="3" required>${job.benefits || ''}</textarea>
                        <small class="form-text text-muted">Separate each benefit with • (bullet symbol)</small>
                    </div>
                    <div class="form-group">
                        <label>What You'll Do *</label>
                        <textarea class="form-control" id="edit-job-what-youll-do" rows="3" required>${job.what_youll_do || ''}</textarea>
                        <small class="form-text text-muted">Separate each responsibility with • (bullet symbol)</small>
                    </div>
                    <div class="form-group">
                        <label>What You'll Need *</label>
                        <textarea class="form-control" id="edit-job-what-youll-need" rows="3" required>${job.what_youll_need || ''}</textarea>
                        <small class="form-text text-muted">Separate each requirement with • (bullet symbol)</small>
                    </div>
                    <div class="form-group">
                        <label>Additional Requirements *</label>
                        <textarea class="form-control" id="edit-job-requirements" rows="3" required>${job.requirements}</textarea>
                    </div>
                    <div class="form-group">
                        <label>Image URL</label>
                        <input type="url" class="form-control" id="edit-job-image" value="${job.image_url || ''}">
                    </div>
                `;
            }

            // Update job
            async function updateJob() {
                const jobId = document.getElementById('edit-job-id').value;

                const formData = {
                    title: document.getElementById('edit-job-title').value.trim(),
                    department: document.getElementById('edit-job-department').value,
                    location: document.getElementById('edit-job-location').value.trim(),
                    type: document.getElementById('edit-job-type').value,
                    description: document.getElementById('edit-job-description').value.trim(),
                    benefits: document.getElementById('edit-job-benefits').value.trim(),
                    what_youll_do: document.getElementById('edit-job-what-youll-do').value.trim(),
                    what_youll_need: document.getElementById('edit-job-what-youll-need').value.trim(),
                    requirements: document.getElementById('edit-job-requirements').value.trim(),
                    image_url: document.getElementById('edit-job-image').value.trim()
                };

                // Validation
                if (!formData.title || !formData.department || !formData.location ||
                    !formData.type || !formData.description || !formData.benefits ||
                    !formData.what_youll_do || !formData.what_youll_need || !formData.requirements) {
                    showMessage('Please fill in all required fields.', 'danger');
                    return;
                }

                try {
                    showUpdateLoading();

                    const response = await fetch(`${ADMIN_API_URL}/${jobId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to update job');
                    }

                    showMessage('Job updated successfully!', 'success');
                    $('#editJobModal').modal('hide');
                    loadJobs(); // Reload the jobs list

                } catch (error) {
                    console.error('Error updating job:', error);
                    showMessage('Error updating job: ' + error.message, 'danger');
                } finally {
                    hideUpdateLoading();
                }
            }

            // Delete job
            async function deleteJob(jobId, jobTitle) {
                if (!confirm(`Are you sure you want to delete the job "${jobTitle}"? This action cannot be undone.`)) {
                    return;
                }

                try {
                    const response = await fetch(`${ADMIN_API_URL}/${jobId}`, {
                        method: 'DELETE'
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'Failed to delete job');
                    }

                    showMessage('Job deleted successfully!', 'success');
                    loadJobs(); // Reload the jobs list

                } catch (error) {
                    console.error('Error deleting job:', error);
                    showMessage('Error deleting job: ' + error.message, 'danger');
                }
            }

            // Update statistics
            function updateStats(jobs) {
                const totalJobs = jobs.length;
                const activeJobs = jobs.length; // All jobs are considered active
                const departments = [...new Set(jobs.map(job => job.department))].length;

                document.getElementById('total-jobs').textContent = totalJobs;
                document.getElementById('active-jobs').textContent = activeJobs;
                document.getElementById('departments').textContent = departments;
            }

            // Reset form
            function resetForm() {
                document.getElementById('job-form').reset();
            }

            // Show/hide loading states
            function showJobsLoading() {
                document.getElementById('jobs-loading').style.display = 'block';
                document.getElementById('jobs-container').innerHTML = '';
            }

            function hideJobsLoading() {
                document.getElementById('jobs-loading').style.display = 'none';
            }

            function showJobsError() {
                document.getElementById('jobs-error').style.display = 'block';
            }

            function hideJobsError() {
                document.getElementById('jobs-error').style.display = 'none';
            }

            function showJobsEmpty() {
                document.getElementById('jobs-empty').style.display = 'block';
            }

            function hideJobsEmpty() {
                document.getElementById('jobs-empty').style.display = 'none';
            }

            function showSubmitLoading() {
                document.getElementById('submit-text').style.display = 'none';
                document.getElementById('submit-loading').style.display = 'inline';
                document.querySelector('#job-form button[type="submit"]').disabled = true;
            }

            function hideSubmitLoading() {
                document.getElementById('submit-text').style.display = 'inline';
                document.getElementById('submit-loading').style.display = 'none';
                document.querySelector('#job-form button[type="submit"]').disabled = false;
            }

            function showUpdateLoading() {
                document.getElementById('update-text').style.display = 'none';
                document.getElementById('update-loading').style.display = 'inline';
                document.querySelector('#editJobModal .btn-primary').disabled = true;
            }

            function hideUpdateLoading() {
                document.getElementById('update-text').style.display = 'inline';
                document.getElementById('update-loading').style.display = 'none';
                document.querySelector('#editJobModal .btn-primary').disabled = false;
            }

            // Show message
            function showMessage(message, type) {
                const messageContainer = document.getElementById('message-container');
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                `;

                messageContainer.appendChild(alertDiv);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 5000);
            }
        </script>
    </body>
</html>
